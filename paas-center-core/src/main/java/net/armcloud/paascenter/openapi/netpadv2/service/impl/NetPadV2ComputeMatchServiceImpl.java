package net.armcloud.paascenter.openapi.netpadv2.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo;
import net.armcloud.paascenter.common.core.exception.BasicException;
import net.armcloud.paascenter.common.core.exception.code.ExceptionCode;
import net.armcloud.paascenter.common.redis.lock.RedissonDistributedLock;
import net.armcloud.paascenter.openapi.exception.code.PadExceptionCode;
import net.armcloud.paascenter.openapi.mapper.DeviceMapper;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.DeviceCodeWithComputeUnitCntDTO;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2ComputeMatchService;
import net.armcloud.paascenter.openapi.netpadv2.service.NetPadV2IpManager;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ComputeUnitCache;
import net.armcloud.paascenter.openapi.service.netstorage.NetStorageComputeUnitService;
import net.armcloud.paascenter.openapi.utils.RedisKeyUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paascenter.openapi.exception.code.PadExceptionCode.NETWORK_INSTANCE_COMPUTE_UNIT_NOT;

/**
 * 网存实例V2算力匹配服务实现
 * 优化版本的算力匹配逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-05
 */
@Slf4j
@Service
public class NetPadV2ComputeMatchServiceImpl implements NetPadV2ComputeMatchService {

    @Resource
    private RedissonDistributedLock redissonDistributedLock;

    @Resource
    private NetStorageComputeUnitService netStorageComputeUnitService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    private final static String deviceComputeUnitCntKey = "netpadv2:compute:device";

    private final static String deviceBoundComputeUnitCntKey = "netpadv2:compute:device:bound";

    private final static String deviceUnboundComputeUnitCntKey = "netpadv2:compute:device:unbound";

    private final static String deviceSetKey = "netpadv2:compute:device:set";

    private final static String unboundDeviceSetKey = "netpadv2:compute:device:unbound:set";

    private final static String noComputeUnitFlagKey = "netpadv2:compute:device:no_compute_unit";

    // 分布式锁配置
    private static final String COMPUTE_LOCK_PREFIX = "netpadv2:compute:match";
    private static final long LOCK_WAIT_TIME = 10L;
    private static final long LOCK_LEASE_TIME = 30L;
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private NetPadV2IpManager netPadV2IpManager;

    @Override
    public List<NetPadRelation> batchMatchComputeAndStorage(String clusterCode,
                                                           List<PadDetailsVO> detailsVOList,
                                                           long customerId) {
        if (CollectionUtils.isEmpty(detailsVOList)) {
            return new ArrayList<>();
        }
        long startTime = System.currentTimeMillis();
        log.info("开始批量匹配算力资源,集群：{},实例数量：{}", clusterCode, detailsVOList.size());

        // 按规格分组
        Map<String, List<PadDetailsVO>> specGroupMap = detailsVOList.stream()
                .collect(Collectors.groupingBy(PadDetailsVO::getDeviceLevel));

        List<NetPadRelation> allResults = new ArrayList<>();

        // 分规格进行算力匹配,使用分布式锁确保并发安全
        for (Map.Entry<String, List<PadDetailsVO>> entry : specGroupMap.entrySet()) {
            String deviceLevel = entry.getKey();
            List<PadDetailsVO> specPads = entry.getValue();
            // 获取未分配算力
            List<NetPadRelation> netPadRelationList;
            try {
                netPadRelationList = getUnboundComputeUnit(clusterCode, deviceLevel, customerId, specPads);
                // 分配给实例
                for (int i = 0; i < specPads.size(); i++) {
                    if (i < netPadRelationList.size()) {
                        NetPadRelation netPadRelation = netPadRelationList.get(i);
                        netPadRelation.setPadCode(specPads.get(i).getPadCode());
                        /* IMPORTANT 获取可用IP */
                        if (StrUtil.isBlank(netPadRelation.getIp())) {
                            String ip = netPadV2IpManager.getAvailableIp(netPadRelation.getArmServerId());
                            netPadRelation.setIp(ip);
                            netStorageComputeUnitService.updateIpByCode(netPadRelation.getComputeUnitCode(), ip);
                        } else {
                            netPadRelation.setIp(netPadRelation.getIp());
                        }
                    } else {
                        NetPadRelation netPadRelation = new NetPadRelation();
                        netPadRelation.setPadCode(specPads.get(i).getPadCode());
                        netPadRelation.setMatchFailException(NETWORK_INSTANCE_COMPUTE_UNIT_NOT);
                        netPadRelation.setMatchSuccess(false);
                        netPadRelationList.add(netPadRelation);
                    }
                }
            } catch (BasicException e) {
                netPadRelationList = new ArrayList<>();
                for (PadDetailsVO specPad : specPads) {
                    NetPadRelation netPadRelation = new NetPadRelation();
                    netPadRelation.setPadCode(specPad.getPadCode());
                    netPadRelation.setMatchFailException(e.getExceptionCode());
                    netPadRelation.setMatchSuccess(false);
                    netPadRelationList.add(netPadRelation);
                }
            }
            allResults.addAll(netPadRelationList);
        }
        log.info("批量算力匹配完成,耗时：{}ms", System.currentTimeMillis() - startTime);
        return allResults;
    }

    /**
     * 获取未分配算力单元（可用算力数量不满足所需数量时会抛出异常）
     * @param clusterCode 集群编码
     * @param deviceLevel 规格
     * @param customerId 用户ID
     * @param padDetailsList 需要分配算力的实例详情列表
     * @return 未分配算力单元列表
     */
    private List<NetPadRelation> getUnboundComputeUnit(String clusterCode, String deviceLevel, Long customerId, List<PadDetailsVO> padDetailsList) {
        int needCnt = padDetailsList.size();
        // 获取可用算力单元
        List<NetPadRelation> result = new ArrayList<>(getAvailableComputeUnit(clusterCode, deviceLevel, customerId, needCnt));
        if (CollectionUtil.isNotEmpty(result) && result.size() == needCnt) {
            return result;
        }

//        String lockKey = COMPUTE_LOCK_PREFIX + clusterCode + ":" + deviceLevel;
        String lockKey = RedisKeyUtils.lockKey(COMPUTE_LOCK_PREFIX, clusterCode, deviceLevel, String.valueOf(customerId));
        RLock lock = redissonDistributedLock.tryLock(lockKey, LOCK_WAIT_TIME, LOCK_LEASE_TIME);

        if (lock == null) {
            log.warn("获取算力匹配锁失败,集群：{},规格：{}, 用户：{}", clusterCode, deviceLevel, customerId);
            throw new BasicException(PadExceptionCode.COMPUTE_UNIT_GET_EXCEPTION);
        }
        try {
            // 重新获取一次
            result.addAll(getAvailableComputeUnit(clusterCode, deviceLevel, customerId, needCnt - result.size()));
            if (CollectionUtil.isNotEmpty(result) && result.size() == needCnt) {
                return result;
            }
            // 走DB查询数据，先获取用户下全部板卡,以及对应算力单元,缓存到redis中,设置2小时过期时间。
            cacheAllDeviceCode(clusterCode, deviceLevel, customerId);
            // 获取可用算力单元
            result.addAll(getAvailableComputeUnit(clusterCode, deviceLevel, customerId, needCnt - result.size()));
            if (CollectionUtil.isEmpty(result)) {
                throw new BasicException(NETWORK_INSTANCE_COMPUTE_UNIT_NOT);
            }
            if (result.size() != needCnt) {
                log.info("可用算力单元数量不足,集群：{},规格：{}, 用户：{}, 当前数量：{}, 需要数量：{}", clusterCode, deviceLevel, customerId, result.size(), needCnt);
            }
            return result;
        } finally {
            redissonDistributedLock.unlock(lock);
        }
    }


    /**
     * 获取可用算力单元，会尽最大努力获取到全部的可用算力单元
     * @param clusterCode 集群编码
     * @param deviceLevel 规格
     * @param customerId 用户ID
     * @param size 需要的数量
     * @return 可用算力单元列表
     */
    private List<NetPadRelation> getAvailableComputeUnit(String clusterCode, String deviceLevel, Long customerId, int size) {
        // 判断空值标记
        if (checkIsEmpty(clusterCode, deviceLevel, customerId)) {
            log.warn("无可用算力单元,集群：{},规格：{}, 用户：{}", clusterCode, deviceLevel, customerId);
            return new ArrayList<>();
        }
        String deviceCode = getDeviceCode(clusterCode, deviceLevel, customerId);
        // 按板卡编号查询可用算力单元
        if (StrUtil.isBlank(deviceCode)) {
            return new ArrayList<>();
        }
        // 查询可用算力单元
        List<NetPadV2ComputeUnitCache> availableComputeUnitByDeviceCode = netStorageComputeUnitService.getAvailableComputeUnitByDeviceCode(deviceCode, size);
        if (availableComputeUnitByDeviceCode.size() < size) {
            List<NetPadRelation> result = computeCodesConvertToNetPadRelation(availableComputeUnitByDeviceCode, deviceCode);
            // 换其他板卡继续查询可用算力单元
            result.addAll(getAvailableComputeUnit(clusterCode, deviceLevel, customerId, size - availableComputeUnitByDeviceCode.size()));
            return result;
        }
        return computeCodesConvertToNetPadRelation(availableComputeUnitByDeviceCode, deviceCode);
    }

    private List<NetPadRelation> computeCodesConvertToNetPadRelation(List<NetPadV2ComputeUnitCache> availableComputeUnitByDeviceCode, String deviceCode) {
        DeviceInfoVo deviceInfo = deviceMapper.getArmServerIdByDeviceCode(deviceCode);
        if (Objects.isNull(deviceInfo)) {
            log.error("获取算力单元失败, 板卡不存在???? 板卡：{}", deviceCode);
            throw new BasicException(PadExceptionCode.COMPUTE_UNIT_GET_EXCEPTION);
        }
        return availableComputeUnitByDeviceCode.stream()
                .map(computeUnit -> {
                    NetPadRelation relation = new NetPadRelation();
                    relation.setComputeUnitCode(computeUnit.getComputeUnitCode());
                    relation.setIp(computeUnit.getIp());
                    relation.setArmServerId(deviceInfo.getArmServerId());
                    relation.setDeviceCode(deviceCode);
                    return relation;
                })
                .collect(Collectors.toList());
    }

    /**
     * 判断是否存在空值标记
     * @param clusterCode 集群编码
     * @param deviceLevel 规格
     * @param customerId 用户ID
     */
    private boolean checkIsEmpty(String clusterCode, String deviceLevel, Long customerId) {
        String noComputeUnitFlagCacheKey = getNoComputeUnitFlagCacheKey(clusterCode, deviceLevel, customerId);
        return redisTemplate.hasKey(noComputeUnitFlagCacheKey);
    }

    @NotNull
    private static String getNoComputeUnitFlagCacheKey(String clusterCode, String deviceLevel, Long customerId) {
        return RedisKeyUtils.cacheKey(noComputeUnitFlagKey, clusterCode, deviceLevel, String.valueOf(customerId));
    }

    private void cacheAllDeviceCode(String clusterCode, String deviceLevel, Long customerId) {
        // 查询全部板卡以及对应算力单元
        String noComputeUnitFlagCacheKey = getNoComputeUnitFlagCacheKey(clusterCode, deviceLevel, customerId);
        List<DeviceCodeWithComputeUnitCntDTO> allDeviceWithComputeUnitCnt = netStorageComputeUnitService.getAllDeviceWithComputeUnitCnt(clusterCode, deviceLevel, customerId);
        if (CollectionUtils.isEmpty(allDeviceWithComputeUnitCnt)) {
            log.info("无可用算力单元,集群：{},规格：{}, 用户：{}", clusterCode, deviceLevel, customerId);
            // 缓存5s空值防止并发
            redisTemplate.opsForValue().set(noComputeUnitFlagCacheKey, "1", 5, TimeUnit.SECONDS);
            return;
        }
        // 调用公共方法维护板卡与算力计数
        maintainDeviceComputeUnitCount(clusterCode, deviceLevel, customerId, allDeviceWithComputeUnitCnt);
    }

    /**
     * 维护板卡与算力计数的公共方法
     * 区分历史被绑定和历史未被绑定的算力数量
     *
     * @param clusterCode 集群编码
     * @param deviceLevel 规格
     * @param customerId 用户ID
     * @param allDeviceWithComputeUnitCnt 板卡算力数据列表
     */
    private void maintainDeviceComputeUnitCount(String clusterCode, String deviceLevel, Long customerId,
                                             List<DeviceCodeWithComputeUnitCntDTO> allDeviceWithComputeUnitCnt) {

        allDeviceWithComputeUnitCnt.forEach(device -> {
            String deviceCode = device.getDeviceCode();

            // 维护总算力计数
            incAllCnt(clusterCode, deviceLevel, customerId, deviceCode, device.getComputeUnitCnt());

            // 维护历史被绑定算力计数
            incBoundUnitCnt(deviceCode, device.getBoundComputeUnitCnt());

            // 维护历史未被绑定算力计数
            incUnBoundUnitCnt(clusterCode, deviceLevel, customerId, deviceCode, device.getUnboundComputeUnitCnt());
        });
    }

    private void incBoundUnitCnt(String deviceCode, Integer device) {
        String boundCntKey = deviceBoundComputeUnitCntKey(deviceCode);
        redisTemplate.opsForValue().increment(boundCntKey, device);
        redisTemplate.expire(boundCntKey, 10, TimeUnit.MINUTES);
    }

    private void incUnBoundUnitCnt(String clusterCode, String deviceLevel, Long customerId, String deviceCode, Integer device) {
        String boundCntKey = deviceUnboundComputeUnitCntKey(deviceCode);
        Long increment = redisTemplate.opsForValue().increment(boundCntKey, device);
        redisTemplate.expire(boundCntKey, 10, TimeUnit.MINUTES);

        // 如果存在历史未被绑定的算力，加入到未绑定板卡集合中
        if (Objects.nonNull(increment) && increment > 0) {
            String unboundDeviceSetCacheKey = getUnboundDeviceSetKey(clusterCode, deviceLevel, customerId);
            redisTemplate.opsForSet().add(unboundDeviceSetCacheKey, deviceCode);
            redisTemplate.expire(unboundDeviceSetCacheKey, 10, TimeUnit.MINUTES);
        }
    }

    private void incAllCnt(String clusterCode, String deviceLevel, Long customerId, String deviceCode, Integer cnt) {
        String cntKey = deviceComputeUnitCntKey(deviceCode);
        redisTemplate.opsForValue().increment(cntKey, cnt);
        redisTemplate.expire(cntKey, 10, TimeUnit.MINUTES);
        String deviceSetCacheKey = getDeviceSetKey(clusterCode, deviceLevel, customerId);
        // 使用set缓存板卡编号
        redisTemplate.opsForSet().add(deviceSetCacheKey, deviceCode);
        redisTemplate.expire(deviceSetCacheKey, 10, TimeUnit.MINUTES);
    }

    @Override
    public void incBoundComputeUnitCnt(String clusterCode, String deviceLevel, Long customerId, String deviceCode) {
        if (notNeedInc(clusterCode, deviceLevel, customerId)) return;
        incAllCnt(clusterCode, deviceLevel, customerId, deviceCode, 1);
        incBoundUnitCnt(deviceCode, 1);
    }

    private boolean notNeedInc(String clusterCode, String deviceLevel, Long customerId) {
        String setKey = getDeviceSetKey(clusterCode, deviceLevel, customerId);
        if (!redisTemplate.hasKey(setKey)) {
            return true;
        }
        Long size = redisTemplate.opsForSet().size(setKey);
        return Objects.isNull(size) || size <= 0;
    }


    @Override
    public boolean removeDeviceCodes(List<String> deviceCodes) {
        boolean result = true;
        for (String deviceCode : deviceCodes) {
            try {
                // 获取集群、规格、用户信息
                DeviceInfoVo deviceInfo = deviceMapper.getDeviceInfoByDeviceCode(deviceCode);
                // 删除set
                String deviceSetCacheKey = getDeviceSetKey(deviceInfo.getClusterCode(), deviceInfo.getDeviceLevel(), deviceInfo.getCustomerId());
                String unboundDeviceSetCacheKey = getUnboundDeviceSetKey(deviceInfo.getClusterCode(), deviceInfo.getDeviceLevel(), deviceInfo.getCustomerId());
                redisTemplate.opsForSet().remove(deviceSetCacheKey, deviceCode);
                redisTemplate.opsForSet().remove(unboundDeviceSetCacheKey, deviceCode);

                // 删除所有相关计数器
                String cntKey = deviceComputeUnitCntKey(deviceCode);
                String boundCntKey = deviceBoundComputeUnitCntKey(deviceCode);
                String unboundCntKey = deviceUnboundComputeUnitCntKey(deviceCode);
                redisTemplate.delete(cntKey);
                redisTemplate.delete(boundCntKey);
                redisTemplate.delete(unboundCntKey);
            } catch (Exception e) {
                log.error("删除板卡缓存失败,板卡：{}", deviceCode, e);
                return false;
            }
        }
        return result;
    }

    private String getDeviceCode(String clusterCode, String deviceLevel, Long customerId) {
        return getDeviceCode(clusterCode, deviceLevel, customerId, null);
    }

    /**
     * 获取板卡编号，支持指定板卡编号
     *
     * @param clusterCode 集群编码
     * @param deviceLevel 规格
     * @param customerId 用户ID
     * @param specifiedDeviceCode 指定的板卡编号，为null时随机获取
     * @return 板卡编号
     */
    private String getDeviceCode(String clusterCode, String deviceLevel, Long customerId, String specifiedDeviceCode) {
        String deviceSetCacheKey = getDeviceSetKey(clusterCode, deviceLevel, customerId);
        if (!redisTemplate.hasKey(deviceSetCacheKey)) {
            return null;
        }

        String deviceCode;
        if (StrUtil.isNotBlank(specifiedDeviceCode)) {
            // 指定板卡编号时，检查历史被绑定的算力计数
            String boundCntKey = deviceBoundComputeUnitCntKey(specifiedDeviceCode);
            Long boundCnt = redisTemplate.opsForValue().decrement(boundCntKey);
            if (Objects.isNull(boundCnt)) {
                deviceCode = getRandomDeviceCode(clusterCode, deviceLevel, customerId);
            } else {
                if (boundCnt < 0) {
                    // 当前板卡下没有历史被绑定的算力，恢复计数器并随机获取一个板卡编号。
                    redisTemplate.opsForValue().increment(boundCntKey);
                    deviceCode = getRandomDeviceCode(clusterCode, deviceLevel, customerId);
                } else {
                    // 如果历史被绑定的算力计数不为0，返回该板卡编号
                    deviceCode = specifiedDeviceCode;
                }
            }
        } else {
            // 随机获取板卡编号，优先获取存在历史未被绑定算力的板卡
            deviceCode = getRandomDeviceCode(clusterCode, deviceLevel, customerId);
        }

        if (StrUtil.isBlank(deviceCode)) {
            return null;
        }

        // 判断计数器并维护
        return processDeviceCodeCount(clusterCode, deviceLevel, customerId, deviceCode);
    }

    /**
     * 随机获取板卡编号，优先获取存在历史未被绑定算力的板卡
     */
    private String getRandomDeviceCode(String clusterCode, String deviceLevel, Long customerId) {
        // 先尝试获取存在历史未被绑定算力的板卡
        String unboundDeviceSetCacheKey = getUnboundDeviceSetKey(clusterCode, deviceLevel, customerId);
        if (redisTemplate.hasKey(unboundDeviceSetCacheKey)) {
            String unboundDeviceCode = redisTemplate.opsForSet().randomMember(unboundDeviceSetCacheKey);
            if (StrUtil.isNotBlank(unboundDeviceCode)) {
                String unboundCntKey = deviceUnboundComputeUnitCntKey(unboundDeviceCode);
                Long decrement = redisTemplate.opsForValue().decrement(unboundCntKey);
                if (Objects.isNull(decrement) || decrement < 0) {
                    // 说明这个板卡的未被绑定的算力单元已经用完了
                    // 恢复计数
                    redisTemplate.opsForValue().increment(unboundCntKey);
                    // 从set中删除
                    redisTemplate.opsForSet().remove(unboundDeviceSetCacheKey, unboundDeviceCode);
                    // 重新获取一个
                    return getRandomDeviceCode(clusterCode, deviceLevel, customerId);
                }
                return unboundDeviceCode;
            }
        }

        // 如果没有历史未被绑定的算力板卡，随机获取一个板卡
        String deviceSetCacheKey = getDeviceSetKey(clusterCode, deviceLevel, customerId);
        return redisTemplate.opsForSet().randomMember(deviceSetCacheKey);
    }

    /**
     * 处理板卡编号的计数逻辑
     */
    private String processDeviceCodeCount(String clusterCode, String deviceLevel, Long customerId, String deviceCode) {
        String cntKey = deviceComputeUnitCntKey(deviceCode);
        Long cnt = redisTemplate.opsForValue().decrement(cntKey);

        if (Objects.isNull(cnt) || cnt <= 0) {
            // 说明这个板卡的算力单元已经用完了,从set中删除
            String deviceSetCacheKey = getDeviceSetKey(clusterCode, deviceLevel, customerId);
            String unboundDeviceSetCacheKey = getUnboundDeviceSetKey(clusterCode, deviceLevel, customerId);

            redisTemplate.opsForSet().remove(deviceSetCacheKey, deviceCode);
            redisTemplate.opsForSet().remove(unboundDeviceSetCacheKey, deviceCode);

            // 删除相关计数器
//            redisTemplate.delete(cntKey);
//            redisTemplate.delete(deviceBoundComputeUnitCntKey(deviceCode));
//            redisTemplate.delete(deviceUnboundComputeUnitCntKey(deviceCode));

            if (Objects.equals(cnt, 0L)) {
                // 为0时代表当前板卡还有1个算力可用，返回该板卡。
                return deviceCode;
            } else {
                // 恢复计数
                redisTemplate.opsForValue().increment(cntKey);
                // 递归调用自己,重新获取一个板卡
                return getDeviceCode(clusterCode, deviceLevel, customerId);
            }
        }
        return deviceCode;
    }

    @NotNull
    private static String getDeviceSetKey(String clusterCode, String deviceLevel, Long customerId) {
        return RedisKeyUtils.cacheKey(deviceSetKey, clusterCode, deviceLevel, String.valueOf(customerId));
    }

    @NotNull
    private static String getUnboundDeviceSetKey(String clusterCode, String deviceLevel, Long customerId) {
        return RedisKeyUtils.cacheKey(unboundDeviceSetKey, clusterCode, deviceLevel, String.valueOf(customerId));
    }

    @NotNull
    private static String deviceComputeUnitCntKey(String deviceCode) {
        return RedisKeyUtils.counterKey(deviceComputeUnitCntKey, deviceCode);
    }

    @NotNull
    private static String deviceBoundComputeUnitCntKey(String deviceCode) {
        return RedisKeyUtils.counterKey(deviceBoundComputeUnitCntKey, deviceCode);
    }

    @NotNull
    private static String deviceUnboundComputeUnitCntKey(String deviceCode) {
        return RedisKeyUtils.counterKey(deviceUnboundComputeUnitCntKey, deviceCode);
    }

    // 内部类,用于封装实例与算力单元以及可用IP的关联关系
    @Data
    public static class NetPadRelation {
        private String padCode;
        private String computeUnitCode;
        private Long armServerId;
        private String deviceCode;
        private String ip;
        private ExceptionCode matchFailException;
        private boolean matchSuccess = true;
    }
}
