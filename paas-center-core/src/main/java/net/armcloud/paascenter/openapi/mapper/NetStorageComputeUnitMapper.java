package net.armcloud.paascenter.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageComputeUnit;
import net.armcloud.paascenter.openapi.netpadv2.dto.DeviceCodeWithComputeUnitCntDTO;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ComputeUnitCache;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/24 19:53
 * @Description:
 */
@Mapper
public interface NetStorageComputeUnitMapper extends BaseMapper<NetStorageComputeUnit> {

    /**
     * 关联查询获取实例对应的已绑定（bind_flag=1）算力单元
     * 用于处理关机失败的实例，使得下次开机时可以使用原有的算力单元
     *
     * @param padCode 实例编码
     * @return 已绑定的算力单元，如果不存在则返回null
     */
    @Select("SELECT u.* " +
            "FROM (" +
            "  SELECT net_storage_compute_unit_code " +
            "  FROM net_storage_compute_unit_pad " +
            "  WHERE pad_code = #{padCode} " +
            "  ORDER BY create_time DESC " +
            "  LIMIT 1 " +
            ") AS m " +
            "JOIN net_storage_compute_unit u " +
            "  ON u.net_storage_compute_unit_code = m.net_storage_compute_unit_code " +
            "  AND u.bind_flag = 1")
    NetStorageComputeUnit selectBoundComputeUnitByPadCode(@Param("padCode") String padCode);

    /**
     * 批量查询可用算力单元
     *
     * @param clusterCode   集群编码
     * @param deviceLevel   规格
     * @param requiredCount 需要的数量
     * @return 可用算力单元列表
     */
    List<String> batchQueryAvailableUnits(@Param("clusterCode") String clusterCode, @Param("deviceLevel") String deviceLevel, @Param("requiredCount") int requiredCount);

    /**
     * 按集群编码 规格 用户ID查询出存在可用算力单元的全部板卡以及对应的算力单元数量
     * 区分历史被绑定和历史未被绑定的算力数量
     *
     * @param clusterCode   集群编码
     * @param deviceLevel   规格
     * @param customerId    客户ID
     * @return 板卡编号以及对应的算力单元数量（包含历史绑定状态区分）
     */
    List<DeviceCodeWithComputeUnitCntDTO> getAllDeviceWithComputeUnitCnt(@Param("clusterCode") String clusterCode, @Param("deviceLevel") String deviceLevel, @Param("customerId") Long customerId);

    /**
     * 批量更新算力单元的绑定状态
     * @param computeUnitCodeList 算力单元编号列表
     * @param bindFlag 绑定状态 0-未绑定 1-已绑定 2-预分配
     * @return 更新成功的数量
     */
    int updateBatchBindFlagByCodeList(@Param("computeUnitCodeList") List<String> computeUnitCodeList, @Param("bindFlag") int bindFlag);

    /**
     * 批量更新算力单元的绑定状态为绑定中
     * @param computeUnitCodeList 算力单元编号列表
     * @return 更新成功的数量
     */
    int updateBindingByCodeList(@Param("computeUnitCodeList") List<String> computeUnitCodeList);


    /**
     * 查询可用算力单元
     * @param deviceCode 板卡编号
     * @return 可用算力单元列表
     */
    List<NetPadV2ComputeUnitCache> queryAvailableComputeUnit(@Param("deviceCode") String deviceCode);
    /**
     * 根据算力单元编号查询算力单元
     * @param computeUnitCode 算力单元编号
     * @return 算力单元
     */
    NetPadV2ComputeUnitCache getComputeUnitByComputeUnitCode(@Param("computeUnitCode") String computeUnitCode);

    List<String> getUsedIpListByArmServerId(@Param("armServerId") Long armServerId);

    int updateIpByCode(@Param("computeUnitCode") String computeUnitCode, @Param("ip") String ip);
}