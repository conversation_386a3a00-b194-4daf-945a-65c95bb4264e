package net.armcloud.paascenter.openapi.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

@Data
public class PadDetailsVO implements Serializable {

    /**
     * 实例Id
     */
    private Long padId;
    /**
     * 云机编号
     */
    private String padCode;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 镜像ID
     */
    private String imageId;

    /**
     * 实例规格
     */
    private String deviceLevel;

    /**
     * 机房编码
     */
    @JsonIgnore
    private String dcId;
    /**
     * 机房信息
     */
    private DcInfoVO dcInfo;

    /**
     * 实例状态
     */
    private Integer padStatus;

    /**
     * 物理机状态
     */
    private Integer deviceStatus;

    /**
     * gameService 连接状态
     */
    private Integer online;

    /**
     * 推流状态
     */
    private Integer streamStatus;

    /**
     * 存储总容量(字节)
     */
    private Long dataSize;

    /**
     * 存储已使用容量(字节)
     */
    private Long dataSizeUsed;

    /**
     * adb开关状态(1 开启 为空为0都是关闭状态)
     */
    private String adbOpenStatus;

    /**
     * 板卡IP
     */
    private String deviceIp;

    /**
     * 屏幕布局编码
     */
    private String screenLayoutCode;

    /**
     * mac地址
     */
    private String mac;

    /**
     * 网存ID
     */
    private String netStorageResId;

    /**
     * 实例dns
     */
    private String dns;
    /**
     * 内存大小
     */
    private Integer memory;

    /**
     * cpu大小
     */
    private Integer cpu;

    /**
     * 网存实例标记(1 网存实例 没值或者为0 本地实例)
     */
    private Integer netStorageResFlag;

    /**
     * 网存实例大小(GB)
     */
    private Long netStorageResSize;

    /**
     * 真实机型模板ID
     */
    private Long realPhoneTemplateId;

    /**
     * 集群编码code
     */
    private String clusterCode;

    /**
     * 实例Ip
     */
    private String  padIp;

    /**
     * 是否指定网存Id开机
     */
    private Boolean updateNetStorageResFlag = false;

    /**
     * 网存ID
     */
    private String targetStorageResId;

    /**
     * armIp
     */
    private String armIp;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 类型（云真机、虚拟机）
     */
    private String type;

    /**
     * 服务器id
     */
    private Long armServerId;

    /**
     * 最后开机使用的算力编号
     */
    private String lastComputeUnitCode;

    /**
     * 最后开机使用的板卡编号
     */
    private String lastDeviceCode;
}
