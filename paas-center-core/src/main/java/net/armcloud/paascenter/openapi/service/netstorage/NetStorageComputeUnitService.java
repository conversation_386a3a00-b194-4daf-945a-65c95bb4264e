package net.armcloud.paascenter.openapi.service.netstorage;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import net.armcloud.paascenter.common.client.internal.vo.DeviceInfoVo;
import net.armcloud.paascenter.common.client.internal.vo.GeneratePadTaskVO;
import net.armcloud.paascenter.common.model.dto.api.NetWorkOnDTO;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.entity.paas.NetStorageComputeUnit;
import net.armcloud.paascenter.openapi.model.dto.NetStorageComputeDTO;
import net.armcloud.paascenter.openapi.model.dto.PowerOffForceDTO;
import net.armcloud.paascenter.openapi.model.dto.netstorage.NetStorageDTO;
import net.armcloud.paascenter.openapi.model.vo.NetStorageComputeVO;
import net.armcloud.paascenter.openapi.model.vo.PadDetailsVO;
import net.armcloud.paascenter.openapi.netpadv2.dto.DeviceCodeWithComputeUnitCntDTO;
import net.armcloud.paascenter.openapi.netpadv2.vo.NetPadV2ComputeUnitCache;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/24 19:57
 * @Description: 网存算力单元service
 */
public interface NetStorageComputeUnitService extends IService<NetStorageComputeUnit> {

    /**
     * 根据板卡生成算力单元
     * @return
     */
    Boolean createNetStorageComputeUnitByDeviceCodeList(List<Device> deviceCodeList, String deviceLevel );

    /**
     * 删除算力单元
     * @param deviceCodeList
     * @return
     */
    Boolean deleteNetStorageComputeUnitByDeviceCodeList(List<String> deviceCodeList);

    /**
     * 根据板卡code获取对应的算力单元是否被绑定占用
     * @param deviceCodeList
     * @return 所有的都没被绑定,返回false,有一个或以上被绑定,返回true
     */
    Boolean NetStorageComputeUnitIsBind(List<String> deviceCodeList);

    /**
     * 根据板卡规格匹配对应(未绑定)数量的算力单元()
     * @param deviceLevel
     * @param padVoList
     * @return 如果当前所用户所拥有的算力<padVoList.size,会正常返回所拥有的数量
     */
    NetStorageDTO matchUnboundComputeUnitsBySpecAndQuantity(String clusterCode ,String deviceLevel, List<PadDetailsVO> padVoList,String countryCode, JSONObject androidProp);

    /**
     * 获取算力单元使用情况
     * @param param
     */
    Map<String, NetStorageComputeVO> retrieveNetStorageComputeUsage(NetStorageComputeDTO param);

    /**
     * 随机获取集群下一个算力,然后获取的它的ip
     * @param clusterCode
     * @return
     */
    DeviceInfoVo obtainAComputingIPAddressInTheCluster (String  clusterCode);
    
    /**
     * 根据padCode获取已绑定的算力单元
     * 用于处理关机失败的实例，使得下一次开机时可以使用原有的算力单元
     * @param padCode 实例编码
     * @return 已绑定的算力单元，如果不存在则返回null
     */
    NetStorageComputeUnit getBoundComputeUnitByPadCode(String padCode);

    /**
     * 组装未释放算力的pad参数
     * @param deviceLevelMap
     * @param clusterCode
     * @param failedShutdownResourceMap
     * @param param
     * @return
     */
    List<NetStorageDTO> getPadComputeUnitExists(Map<String, List<PadDetailsVO>> deviceLevelMap, String clusterCode, Map<String, NetStorageComputeUnit> failedShutdownResourceMap, NetWorkOnDTO param);

    /**
     * 强制关机
     * @param param
     * @return
     */
    List<GeneratePadTaskVO> powerOffForce(@Valid PowerOffForceDTO param);

    /**
     * 批量查询可用算力单元
     * @param clusterCode 集群编码
     * @param deviceLevel 规格
     * @param requiredCount 需要的数量
     * @return 可用算力单元列表
     */
    List<String> batchQueryAvailableUnits(String clusterCode, String deviceLevel, int requiredCount);

    /**
     * 按集群编码 规格 用户ID查询出存在可用算力单元的全部板卡以及对应的算力单元数量
     *
     * @param clusterCode   集群编码
     * @param deviceLevel   规格
     * @param customerId    客户ID
     * @return 板卡编号以及对应的算力单元数量
     */
    List<DeviceCodeWithComputeUnitCntDTO> getAllDeviceWithComputeUnitCnt(String clusterCode, String deviceLevel, Long customerId);

    /**
     * 根据板卡编号获取可用算力单元
     * 会缓存当前板卡的算力单元，防止高并发下重复查询数据库。
     * @param deviceCode 板卡编号
     * @param size 需要的数量
     * @return 可用算力单元列表（数量可能小于size）
     */
    List<NetPadV2ComputeUnitCache> getAvailableComputeUnitByDeviceCode(String deviceCode, int size);

    /**
     * 批量更新算力单元的绑定状态
     * @param computeUnitCodeList 算力单元编号列表
     * @param bindFlag 绑定状态 0-未绑定 1-已绑定 2-预分配
     */
    int updateBatchBindFlagByCodeList(List<String> computeUnitCodeList, int bindFlag);

    List<String> getUsedIpListByArmServerId(Long armServerId);

    int updateIpByCode(String computeUnitCode, String ip);

    NetStorageComputeUnit getComputeUnitByCode(String computeUnitCode);
}
