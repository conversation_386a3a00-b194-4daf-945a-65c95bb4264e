package net.armcloud.paascenter.openapi.netpadv2.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DeviceCodeWithComputeUnitCntDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 板卡编号 */
    private String deviceCode;

    /** 算力单元数量 */
    private Integer computeUnitCnt;

    /** 历史被绑定的算力数量 */
    private Integer boundComputeUnitCnt;

    /** 历史未被绑定的算力数量 */
    private Integer unboundComputeUnitCnt;
}
