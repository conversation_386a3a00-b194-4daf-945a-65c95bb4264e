package net.armcloud.paascenter.openapi.netpadv2.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.armcloud.paascenter.openapi.netpadv2.entity.NetPadLastOnParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 网存实例开机参数记录表 Mapper接口
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Mapper
public interface NetPadLastOnParamMapper extends BaseMapper<NetPadLastOnParam> {

    /**
     * 批量插入或更新开机参数记录
     * 
     * @param records 开机参数记录列表
     * @return 影响行数
     */
    int batchInsertOrUpdate(@Param("records") List<NetPadLastOnParam> records);

    /**
     * 更新开机成功状态和耗时
     * 
     * @param padCode 实例编码
     * @param bootOnSuccess 开机是否成功
     * @return 影响行数
     */
    int updateBootOnSuccess(@Param("padCode") String padCode, 
                           @Param("bootOnSuccess") Integer bootOnSuccess);

    /**
     * 更新关机时间
     * 
     * @param padCode 实例编码
     * @param offTime 关机时间
     * @return 影响行数
     */
    int updateOffTime(@Param("padCode") String padCode, @Param("offTime") Date offTime);

    /**
     * 根据实例编码删除记录
     * 
     * @param padCode 实例编码
     * @return 影响行数
     */
    int deleteByPadCode(@Param("padCode") String padCode);

    /**
     * 根据实例编码查询开机参数记录
     * 
     * @param padCode 实例编码
     * @return 开机参数记录
     */
    NetPadLastOnParam selectByPadCode(@Param("padCode") String padCode);

    /**
     * 批量查询开机参数记录
     * 
     * @param padCodes 实例编码列表
     * @return 开机参数记录列表
     */
    List<NetPadLastOnParam> selectByPadCodes(@Param("padCodes") List<String> padCodes);
}
