-- 网存实例开机参数记录表
-- 记录网存实例开机时使用的存储、算力、板卡编号、板卡IP、集群、服务器，
-- 以及开机是否成功、开机耗时、开机时间、关机时间

CREATE TABLE `net_pad_last_on_param` (
    `pad_code` varchar(64) NOT NULL COMMENT '实例编码，主键',
    `device_code` varchar(64) DEFAULT NULL COMMENT '板卡编号',
    `device_ip` varchar(32) DEFAULT NULL COMMENT '板卡IP',
    `cluster_code` varchar(64) DEFAULT NULL COMMENT '集群编码',
    `arm_server_code` varchar(64) DEFAULT NULL COMMENT '服务器编码',
    `compute_unit_code` varchar(64) DEFAULT NULL COMMENT '算力编号',
    `boot_on_success` tinyint(1) DEFAULT NULL COMMENT '开机是否成功 0:失败 1:成功。成功时代表从创建任务到上报健康的时间差，失败代表任务返回失败或超时。',
    `boot_on_run_time` bigint(20) DEFAULT NULL COMMENT '开机耗时（秒）',
    `boot_on_time` datetime DEFAULT NULL COMMENT '开机时间',
    `off_time` datetime DEFAULT NULL COMMENT '关机时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`pad_code`),
    KEY `idx_device_code` (`device_code`),
    KEY `idx_compute_unit_code` (`compute_unit_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网存实例开机参数记录表';
